import { ref, computed } from 'vue';

// 价格类型枚举
export enum PriceType {
  FREE = 'free',           // 免费
  ONE_TIME = 'one_time',   // 一口价
  MONTHLY = 'monthly',     // 包月
  PER_USE = 'per_use'      // 每次
}

// 应用分类
export enum AppCategory {
  IMAGE_PROCESSING = 'image_processing',  // 图像处理
  DATA_ANALYSIS = 'data_analysis',        // 数据分析
  SEO_TOOLS = 'seo_tools',               // SEO工具
  MARKET_ANALYSIS = 'market_analysis',    // 市场分析
  MANAGEMENT_TOOLS = 'management_tools',  // 管理工具
  AUTOMATION = 'automation',              // 自动化工具
  CONTENT_CREATION = 'content_creation'   // 内容创作
}

// 应用状态
export enum AppStatus {
  ACTIVE = 'active',       // 正常
  MAINTENANCE = 'maintenance', // 维护中
  DEPRECATED = 'deprecated'    // 已废弃
}

// 价格信息接口
export interface PriceInfo {
  type: PriceType;
  amount: number;          // 价格金额
  currency: string;        // 货币单位
  originalAmount?: number; // 原价（用于显示折扣）
  unit?: string;          // 单位说明（如：/月、/次）
  description?: string;    // 价格说明
}

// 应用信息接口
export interface AppInfo {
  id: string;
  name: string;
  description: string;
  longDescription?: string;
  icon: string;
  screenshots: string[];
  category: AppCategory;
  tags: string[];
  price: PriceInfo;
  rating: number;          // 评分 (0-5)
  reviewCount: number;     // 评价数量
  downloadCount: number;   // 下载/使用次数
  developer: string;       // 开发者
  version: string;         // 版本号
  lastUpdated: string;     // 最后更新时间
  status: AppStatus;
  features: string[];      // 功能特性列表
  requirements?: string[]; // 使用要求
  isInstalled?: boolean;   // 是否已安装
  isFavorited?: boolean;   // 是否已收藏
}

// 筛选条件接口
export interface FilterOptions {
  category?: AppCategory;
  priceType?: PriceType;
  rating?: number;         // 最低评分
  searchKeyword?: string;
  sortBy?: 'name' | 'rating' | 'downloadCount' | 'lastUpdated' | 'price';
  sortOrder?: 'asc' | 'desc';
}

// 应用市场状态
const apps = ref<AppInfo[]>([]);
const favoriteApps = ref<string[]>([]);
const installedApps = ref<string[]>([]);
const loading = ref(false);
const currentFilter = ref<FilterOptions>({});

// 模拟应用数据
const mockApps: AppInfo[] = [
  {
    id: 'smart-crop-pro',
    name: '智能裁图专业版',
    description: '基于AI的智能图片裁剪工具，支持批量处理和多种裁剪模式',
    longDescription: '智能裁图专业版是一款基于深度学习的图片裁剪工具，能够自动识别图片主体，进行智能裁剪。支持批量处理、多种裁剪比例、自定义裁剪区域等功能。',
    icon: '🎨',
    screenshots: [
      'https://picsum.photos/800/600?random=1',
      'https://picsum.photos/800/600?random=2',
      'https://picsum.photos/800/600?random=3'
    ],
    category: AppCategory.IMAGE_PROCESSING,
    tags: ['AI', '图片处理', '批量处理', '智能裁剪'],
    price: {
      type: PriceType.MONTHLY,
      amount: 29.9,
      currency: 'CNY',
      originalAmount: 39.9,
      unit: '/月',
      description: '包含所有高级功能，无限制使用'
    },
    rating: 4.8,
    reviewCount: 1256,
    downloadCount: 15420,
    developer: 'RiinAI团队',
    version: '2.1.0',
    lastUpdated: '2024-01-15',
    status: AppStatus.ACTIVE,
    features: [
      '智能主体识别',
      '批量处理支持',
      '多种裁剪比例',
      '高质量输出',
      '云端处理'
    ],
    requirements: ['需要网络连接', '支持JPG/PNG格式']
  },
  {
    id: 'price-monitor-basic',
    name: '价格监控基础版',
    description: '实时监控商品价格变化，支持多平台价格对比',
    longDescription: '价格监控基础版帮助您实时跟踪商品价格变化，支持Amazon、Temu、Shein等主流电商平台。提供价格历史图表、降价提醒等功能。',
    icon: '📊',
    screenshots: [
      'https://picsum.photos/800/600?random=4',
      'https://picsum.photos/800/600?random=5'
    ],
    category: AppCategory.DATA_ANALYSIS,
    tags: ['价格监控', '数据分析', '电商', '提醒'],
    price: {
      type: PriceType.FREE,
      amount: 0,
      currency: 'CNY',
      description: '免费版本，每日可监控10个商品'
    },
    rating: 4.2,
    reviewCount: 892,
    downloadCount: 8750,
    developer: '第三方开发者',
    version: '1.5.2',
    lastUpdated: '2024-01-10',
    status: AppStatus.ACTIVE,
    features: [
      '多平台支持',
      '价格历史图表',
      '降价提醒',
      '数据导出'
    ],
    requirements: ['需要网络连接']
  },
  {
    id: 'keyword-research-pro',
    name: '关键词研究专家',
    description: '专业的关键词挖掘和分析工具，助力SEO优化',
    longDescription: '关键词研究专家是一款专业的SEO工具，提供关键词挖掘、竞争度分析、搜索量预测等功能。帮助您找到高价值的关键词，提升网站排名。',
    icon: '🔍',
    screenshots: [
      'https://picsum.photos/800/600?random=6',
      'https://picsum.photos/800/600?random=7',
      'https://picsum.photos/800/600?random=8'
    ],
    category: AppCategory.SEO_TOOLS,
    tags: ['SEO', '关键词', '搜索优化', '竞争分析'],
    price: {
      type: PriceType.ONE_TIME,
      amount: 199,
      currency: 'CNY',
      originalAmount: 299,
      description: '一次购买，终身使用'
    },
    rating: 4.6,
    reviewCount: 567,
    downloadCount: 3240,
    developer: 'SEO专家团队',
    version: '3.0.1',
    lastUpdated: '2024-01-12',
    status: AppStatus.ACTIVE,
    features: [
      '关键词挖掘',
      '竞争度分析',
      '搜索量预测',
      '长尾词推荐',
      '数据报告'
    ]
  },
  {
    id: 'background-remover',
    name: '一键抠图工具',
    description: 'AI驱动的背景移除工具，一键生成透明背景图片',
    longDescription: '一键抠图工具使用先进的AI算法，能够精确识别图片主体，自动移除背景。支持批量处理，输出高质量透明背景图片。',
    icon: '✂️',
    screenshots: [
      'https://picsum.photos/800/600?random=9',
      'https://picsum.photos/800/600?random=10'
    ],
    category: AppCategory.IMAGE_PROCESSING,
    tags: ['AI', '抠图', '背景移除', '图片处理'],
    price: {
      type: PriceType.PER_USE,
      amount: 0.5,
      currency: 'CNY',
      unit: '/张',
      description: '按使用次数计费，高质量处理'
    },
    rating: 4.9,
    reviewCount: 2341,
    downloadCount: 28750,
    developer: 'RiinAI团队',
    version: '1.8.0',
    lastUpdated: '2024-01-14',
    status: AppStatus.ACTIVE,
    features: [
      'AI智能识别',
      '高精度抠图',
      '批量处理',
      '多格式支持',
      '云端处理'
    ]
  },
  {
    id: 'competitor-analyzer',
    name: '竞品分析大师',
    description: '深度分析竞争对手策略，洞察市场机会',
    longDescription: '竞品分析大师帮助您深入了解竞争对手的产品策略、价格策略、营销手段等。提供详细的分析报告和市场洞察。',
    icon: '🎯',
    screenshots: [
      'https://picsum.photos/800/600?random=11',
      'https://picsum.photos/800/600?random=12'
    ],
    category: AppCategory.MARKET_ANALYSIS,
    tags: ['竞品分析', '市场研究', '策略分析', '商业智能'],
    price: {
      type: PriceType.MONTHLY,
      amount: 99,
      currency: 'CNY',
      unit: '/月',
      description: '专业版功能，深度分析报告'
    },
    rating: 4.4,
    reviewCount: 423,
    downloadCount: 1890,
    developer: '商业分析专家',
    version: '2.3.0',
    lastUpdated: '2024-01-08',
    status: AppStatus.ACTIVE,
    features: [
      '竞品监控',
      '价格对比',
      '营销策略分析',
      '市场趋势预测',
      '定制报告'
    ]
  }
];

// 初始化数据
const initializeApps = () => {
  apps.value = mockApps.map(app => ({
    ...app,
    isFavorited: favoriteApps.value.includes(app.id),
    isInstalled: installedApps.value.includes(app.id)
  }));
};

// 获取所有应用
export const getAllApps = () => {
  return apps.value;
};

// 获取收藏的应用
export const getFavoriteApps = computed(() => {
  return apps.value.filter(app => app.isFavorited);
});

// 根据筛选条件获取应用
export const getFilteredApps = computed(() => {
  let filtered = apps.value;
  const filter = currentFilter.value;

  // 搜索关键词
  if (filter.searchKeyword) {
    const keyword = filter.searchKeyword.toLowerCase();
    filtered = filtered.filter(app =>
      app.name.toLowerCase().includes(keyword) ||
      app.description.toLowerCase().includes(keyword) ||
      app.tags.some(tag => tag.toLowerCase().includes(keyword))
    );
  }

  // 分类筛选
  if (filter.category) {
    filtered = filtered.filter(app => app.category === filter.category);
  }

  // 价格类型筛选
  if (filter.priceType) {
    filtered = filtered.filter(app => app.price.type === filter.priceType);
  }

  // 评分筛选
  if (filter.rating !== undefined) {
    filtered = filtered.filter(app => app.rating >= filter.rating!);
  }

  // 排序
  if (filter.sortBy) {
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;
      
      switch (filter.sortBy) {
        case 'name':
          aValue = a.name;
          bValue = b.name;
          break;
        case 'rating':
          aValue = a.rating;
          bValue = b.rating;
          break;
        case 'downloadCount':
          aValue = a.downloadCount;
          bValue = b.downloadCount;
          break;
        case 'lastUpdated':
          aValue = new Date(a.lastUpdated);
          bValue = new Date(b.lastUpdated);
          break;
        case 'price':
          aValue = a.price.amount;
          bValue = b.price.amount;
          break;
        default:
          return 0;
      }

      if (filter.sortOrder === 'desc') {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      } else {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      }
    });
  }

  return filtered;
});

// 设置筛选条件
export const setFilter = (filter: Partial<FilterOptions>) => {
  currentFilter.value = { ...currentFilter.value, ...filter };
};

// 清除筛选条件
export const clearFilter = () => {
  currentFilter.value = {};
};

// 收藏/取消收藏应用
export const toggleFavorite = (appId: string) => {
  const app = apps.value.find(a => a.id === appId);
  if (!app) return false;

  const index = favoriteApps.value.indexOf(appId);
  if (index > -1) {
    favoriteApps.value.splice(index, 1);
    app.isFavorited = false;
  } else {
    favoriteApps.value.push(appId);
    app.isFavorited = true;
  }

  // 保存到本地存储
  localStorage.setItem('app-market-favorites', JSON.stringify(favoriteApps.value));
  return app.isFavorited;
};

// 安装/卸载应用
export const toggleInstall = (appId: string) => {
  const app = apps.value.find(a => a.id === appId);
  if (!app) return false;

  const index = installedApps.value.indexOf(appId);
  if (index > -1) {
    installedApps.value.splice(index, 1);
    app.isInstalled = false;
  } else {
    installedApps.value.push(appId);
    app.isInstalled = true;
  }

  // 保存到本地存储
  localStorage.setItem('app-market-installed', JSON.stringify(installedApps.value));
  return app.isInstalled;
};

// 获取应用详情
export const getAppById = (appId: string) => {
  return apps.value.find(app => app.id === appId);
};

// 获取分类列表
export const getCategories = () => {
  return Object.values(AppCategory);
};

// 获取价格类型列表
export const getPriceTypes = () => {
  return Object.values(PriceType);
};

// 初始化应用市场
export const initAppMarket = () => {
  loading.value = true;
  
  // 从本地存储加载收藏和安装记录
  const savedFavorites = localStorage.getItem('app-market-favorites');
  if (savedFavorites) {
    favoriteApps.value = JSON.parse(savedFavorites);
  }

  const savedInstalled = localStorage.getItem('app-market-installed');
  if (savedInstalled) {
    installedApps.value = JSON.parse(savedInstalled);
  }

  // 初始化应用数据
  initializeApps();
  
  loading.value = false;
};

// 导出状态
export const appMarketStore = {
  apps: computed(() => apps.value),
  favoriteApps,
  installedApps,
  loading: computed(() => loading.value),
  currentFilter: computed(() => currentFilter.value)
};
